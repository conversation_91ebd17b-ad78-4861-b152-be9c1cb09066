{
    "editor.fontFamily": "Cascadia Code,FiraCode Nerd <PERSON>ont,Comic Shanns,Maple Mono,Agave,Operator Mono <PERSON>,'OPPOSans_OS_1.4',consolas,Consolas, 'Courier New', monospace,Martian Mono",
    "editor.fontSize": 13,
    "editor.fontLigatures": true,
    "editor.wordWrap": "on",
    "editor.formatOnSave": true,
    "editor.linkedEditing": true,
    "editor.cursorBlinking": "expand",
    "editor.smoothScrolling": true,
    "editor.mouseWheelScrollSensitivity": 2,
    "editor.fastScrollSensitivity": 6,
    "editor.stickyScroll.enabled": false,
    "editor.suggestSelection": "recentlyUsedByPrefix",
    "editor.quickSuggestions": {
        "comments": "on",
        "strings": true
    },
    "editor.autoClosingBrackets": "beforeWhitespace",
    "editor.autoClosingDelete": "always",
    "editor.autoClosingOvertype": "always",
    "editor.autoClosingQuotes": "beforeWhitespace",
    "editor.bracketPairColorization.independentColorPoolPerBracketType": true,
    "editor.guides.bracketPairs": true,
    "editor.wordSeparators": "`~!@$%^&*()=+[{]}\\|;:'\",.<>/?",
    "editor.minimap.autohide": true,
    "editor.minimap.maxColumn": 100,
    "editor.minimap.size": "fit",
    "editor.minimap.renderCharacters": false,
    "files.autoSave": "onFocusChange",
    "security.workspace.trust.enabled": false,
    "terminal.integrated.cursorBlinking": true,
    "terminal.integrated.cursorStyle": "line",
    "terminal.integrated.fontSize": 13.5,
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "gitlens.hovers.currentLine.over": "line",
    "gitlens.hovers.currentLine.changes": false,
    "gitlens.hovers.annotations.changes": false,
    "gitlens.hovers.enabled": false,
    "errorLens.enabledDiagnosticLevels": [
        "error",
        "warning"
    ],
    "errorLens.gutterIconsEnabled": true,
    "highlight-matching-tag.styles": {
        "opening": {
            "name": {
                "highlight": "#E5C072",
                "custom": {
                    "color": "white",
                    "fontWeight": "bold",
                    "overviewRulerColor": "yellow"
                }
            }
        },
        "closing": {
            "name": {
                "highlight": "#E5C072",
                "custom": {
                    "color": "white",
                    "fontWeight": "bold",
                    "overviewRulerColor": "yellow"
                }
            }
        }
    },
    "highlight-matching-tag.highlightFromAttributes": false,
    "cSpell.userWords": [],
    "highlightOnCopy.backgroundColor": "rgba(230, 97, 89, 0.9)",
    "highlightOnCopy.foregroundColor": "#fff",
    "highlightOnCopy.timeout": 350,
    "workbench.colorTheme": "One Monokai",
    "workbench.list.smoothScrolling": true,
    "workbench.tree.indent": 10,
    "explorer.compactFolders": false,
    "workbench.editor.customLabels.patterns": {
        "**/index.vue": "${dirname}.vue",
        "**/index.js": "${dirname}.js",
        "**/index.ts": "${dirname}.ts",
        "**/index.jsx": "${dirname}.jsx",
        "**/index.tsx": "${dirname}.tsx"
    },
    "workbench.colorCustomizations": {
        "[Two Monokai]": {
            "sideBarSectionHeader.background": "#2D333F",
            "sideBarSectionHeader.foreground": "#fff",
            "list.hoverBackground": "#37424f",
            "list.activeSelectionBackground": "#3e5c6a",
            "list.inactiveSelectionBackground": "#3e5c6a",
            "list.focusOutline": "#3e5c6a",
            "editorIndentGuide.background1": "#FFFFFF50",
            "editorIndentGuide.activeBackground1": "#FFFF33",
            "editorCursor.foreground": "#fff",
            "editorCursor.background": "#fff"
        }
    },
    "search.exclude": {
        "**/node_modules": true,
        "**/pnpm-lock.yaml": true,
        "**/package-lock.json": true,
        "**/yarn.lock": true,
        "**/build": true,
        "**/dist": true,
        "**/tmp": true,
        "**/.DS_Store": true,
        "**/.git": true,
        "**/.gitignore": true,
        "**/.idea": true,
        "**/.svn": true,
        "**/.vscode": true
    },
    "errorLens.messageTemplate": "[$count]$severity $source: $message [$code]",
    "errorLens.severityText": [
        "⛔",
        "⚠️",
        "🔵INFO",
        "🟢HINT"
    ],
    "errorLens.delay": 500,
    "errorLens.delayMode": "old",
    "errorLens.gutterIconSet": "squareRounded",
    "errorLens.messageBackgroundMode": "message",
    "errorLens.borderRadius": "2em",
    "errorLens.padding": "3px 10px",
    "errorLens.fontSize": "-1.5px",
    "errorLens.margin": "2ch",
    "chat.editor.fontSize": 12,
    "errorLens.excludeByMessage": [
        "is declared but its value is never read",
        "␍⏎"
    ],
    "errorLens.problemRangeDecorationEnabled": true,
    "augment.completions.enableAutomaticCompletions": false,
    "gitlens.ai.model": "vscode",
    "gitlens.ai.vscode.model": "copilot:claude-sonnet-4",
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "workbench.activityBar.orientation": "vertical",
    "workbench.iconTheme": "material-icon-theme",
    "cursor.composer.shouldChimeAfterChatFinishes": true,
    "cursor.composer.shouldAllowCustomModes": true,
    "cursor.general.disableHttp2": true,
    "http.proxy": "http://127.0.0.1:7890",
    "augment.chat.userGuidelines": "",
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "augment.nextEdit.enableBackgroundSuggestions": false,
    "augment.completions.enableQuickSuggestions": false,
    "augment.nextEdit.enableAutoApply": false,
    "update.releaseTrack": "prerelease",
    "window.title": "${dirty}${rootName}",
    "claudeCodeChat.thinking.intensity": "think",
    //"extensions.gallery.serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery",
}
{
    // ==========================================
    // AI 智能助手配置
    // ==========================================
    "github.copilot.nextEditSuggestions.enabled": true,
    // ==========================================
    // 编辑器核心配置
    // ==========================================
    // 字体设置
    "editor.fontFamily": "Cascadia Code,FiraCode Nerd Font,Comic Shanns,Maple Mono,Agave,Operator Mono Lig,'OPPOSans_OS_1.4',consolas,Consolas, 'Courier New', monospace,Martian Mono",
    "editor.fontSize": 13,
    "editor.fontLigatures": true, // 启用编程字体连字
    // 编辑行为
    "editor.wordWrap": "on", // 自动换行
    "editor.formatOnSave": true, // 保存时格式化
    "editor.linkedEditing": true, // 同步编辑 HTML 标签
    // 光标和滚动
    "editor.cursorBlinking": "expand", // 光标闪烁动画
    "editor.smoothScrolling": true, // 平滑滚动
    "editor.mouseWheelScrollSensitivity": 2, // 鼠标滚轮灵敏度
    "editor.fastScrollSensitivity": 6, // 快速滚动灵敏度
    "editor.stickyScroll.enabled": false, // 粘性滚动（函数名固定顶部）
    // 自动补全和建议
    "editor.suggestSelection": "recentlyUsedByPrefix", // 建议选择策略
    "editor.quickSuggestions": {
        "comments": "on", // 注释中启用建议
        "strings": true // 字符串中启用建议
    },
    // 括号和引号
    "editor.autoClosingBrackets": "beforeWhitespace",
    "editor.autoClosingDelete": "always",
    "editor.autoClosingOvertype": "always",
    "editor.autoClosingQuotes": "beforeWhitespace",
    "editor.bracketPairColorization.independentColorPoolPerBracketType": true, // 括号对颜色化
    "editor.guides.bracketPairs": true, // 显示括号对导向线
    // 其他编辑器功能
   "editor.wordSeparators": "`~!@$%^&*()=+[{]}\\|;:'\",.<>/?",
    // 小地图配置
    "editor.minimap.autohide": true, // 自动隐藏小地图
    "editor.minimap.maxColumn": 100, // 小地图最大列数
    "editor.minimap.size": "fit", // 小地图大小自适应
    "editor.minimap.renderCharacters": false, // 只显示色块不显示字符
    // ==========================================
    // 文件和工作区配置
    // ==========================================
    "files.autoSave": "onFocusChange", // 失去焦点时自动保存
    "security.workspace.trust.enabled": false, // 禁用工作区信任提示
    // ==========================================
    // 终端配置
    // ==========================================
    "terminal.integrated.cursorBlinking": true, // 终端光标闪烁
    "terminal.integrated.cursorStyle": "line", // 终端光标样式
    "terminal.integrated.fontSize": 13.5, // 终端字体大小
    // "terminal.integrated.fontFamily": "Comic Shanns,'OPPOSans_OS_1.4','FiraCode Nerd Font',consolas",
    // ==========================================
    // 语言特定配置
    // ==========================================
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    // ==========================================
    // 扩展插件配置
    // ==========================================
    // GitLens 配置
    "gitlens.hovers.currentLine.over": "line",
    "gitlens.hovers.currentLine.changes": false,
    "gitlens.hovers.annotations.changes": false,
    "gitlens.hovers.enabled": false, // 禁用悬停提示
    // Error Lens 配置
    "errorLens.enabledDiagnosticLevels": [
        "error", // 显示错误
        "warning" // 显示警告
    ],
    "errorLens.gutterIconsEnabled": true, // 启用侧边栏图标
    // Highlight Matching Tag 配置
    "highlight-matching-tag.styles": {
        "opening": {
            "name": {
                "highlight": "#E5C072",
                "custom": {
                    "color": "white",
                    "fontWeight": "bold",
                    "overviewRulerColor": "yellow"
                }
            }
        },
        "closing": {
            "name": {
                "highlight": "#E5C072",
                "custom": {
                    "color": "white",
                    "fontWeight": "bold",
                    "overviewRulerColor": "yellow"
                }
            }
        }
    },
    "highlight-matching-tag.highlightFromAttributes": false,
    // 拼写检查器自定义词典
    "cSpell.userWords": [],
    // 复制高亮配置
    "highlightOnCopy.backgroundColor": "rgba(230, 97, 89, 0.9)", // 复制时高亮背景色
    "highlightOnCopy.foregroundColor": "#fff", // 复制时文字颜色
    "highlightOnCopy.timeout": 350, // 高亮持续时间（毫秒）
    // ==========================================
    // 工作台外观配置
    // ==========================================
    "workbench.colorTheme": "Two Monokai", // 主题
    "workbench.list.smoothScrolling": true, // 列表平滑滚动
    "workbench.tree.indent": 10, // 文件夹缩进
    "explorer.compactFolders": false, // 禁用紧凑文件夹显示
    // 自定义文件标签模式（将 index 文件显示为文件夹名）
    "workbench.editor.customLabels.patterns": {
        "**/index.vue": "${dirname}.vue",
        "**/index.js": "${dirname}.js",
        "**/index.ts": "${dirname}.ts",
        "**/index.jsx": "${dirname}.jsx",
        "**/index.tsx": "${dirname}.tsx"
    },
    // One Monokai 主题自定义颜色
    "workbench.colorCustomizations": {
        "[Two Monokai]": {
            // "editor.background": "#363C48", // 编辑器背景
            // "sideBar.background": "#2D333F", // 侧边栏背景
            "sideBarSectionHeader.background": "#2D333F", // 侧边栏标题背景
            "sideBarSectionHeader.foreground": "#fff", // 侧边栏标题文字
            "list.hoverBackground": "#37424f", // 悬停背景
            "list.activeSelectionBackground": "#3e5c6a", // 选中背景
            "list.inactiveSelectionBackground": "#3e5c6a", // 非活动选中背景
            "list.focusOutline": "#3e5c6a", // 选中项边框
            "editorIndentGuide.background1": "#FFFFFF50", // 缩进线
            "editorIndentGuide.activeBackground1": "#FFFF33", // 活动缩进线
            "editorCursor.foreground": "#fff", // 光标前景
            "editorCursor.background": "#fff" // 光标背景
        }
    },
    // ==========================================
    // 搜索配置
    // ==========================================
    "search.exclude": {
        // 依赖包和锁文件
        "**/node_modules": true,
        "**/pnpm-lock.yaml": true,
        "**/package-lock.json": true,
        "**/yarn.lock": true,
        // 构建和输出目录
        "**/build": true,
        "**/dist": true,
        "**/tmp": true,
        // 系统和配置文件
        "**/.DS_Store": true,
        "**/.git": true,
        "**/.gitignore": true,
        "**/.idea": true,
        "**/.svn": true,
        "**/.vscode": true
    },
    "workbench.iconTheme": "mosmmy-icons-vscode",
    "errorLens.messageTemplate": "[$count]$severity $source: $message [$code]",
    "errorLens.severityText": [
        "⛔",
        "⚠️",
        "🔵INFO",
        "🟢HINT"
    ],
    "errorLens.delay": 500,
    "errorLens.delayMode": "old",
    "errorLens.gutterIconSet": "squareRounded",
    "errorLens.messageBackgroundMode": "message", // 只高亮消息背景
    "errorLens.borderRadius": "2em", // 圆角
    "errorLens.padding": "3px 10px", // 内边距
    "errorLens.fontSize": "-1.5px", // 相对编辑器字体小2px
    "errorLens.margin": "2ch", // 增加间距,
    "chat.editor.fontSize": 12,
    "errorLens.excludeByMessage": [
        "is declared but its value is never read",
        "␍⏎"
    ],
    "errorLens.problemRangeDecorationEnabled": true,
    "augment.completions.enableAutomaticCompletions": false,
    // copilot禁用tab
    "github.copilot.enable": {
        "*": false
    },
    "gitlens.ai.model": "vscode",
    "gitlens.ai.vscode.model": "copilot:claude-sonnet-4",
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "augment.completions.enableQuickSuggestions": false,
    "augment.chat.userGuidelines": "使用中文回答和思考",
    "http.proxy": "http://127.0.0.1:7890",
    "window.title": "${dirty}${rootName}",
}
﻿[
    {
        "version":  "0.517.0",
        "name":  "vscode-augment",
        "publisher":  "Augment",
        "source":  "Directory-Enhanced",
        "description":  "Augment yourself with the best AI pair programmer",
        "extensionId":  "Augment.vscode-augment",
        "displayName":  "Augment"
    },
    {
        "version":  "0.5.2",
        "name":  "one-monokai",
        "publisher":  "azemoh",
        "source":  "Directory-Enhanced",
        "description":  "A cross between Monokai and One Dark theme",
        "extensionId":  "azemoh.one-monokai",
        "displayName":  "One Monokai Theme"
    },
    {
        "version":  "0.14.25",
        "name":  "vscode-tailwindcss",
        "publisher":  "bradlc",
        "source":  "Directory-Enhanced",
        "description":  "Intelligent Tailwind CSS tooling for VS Code",
        "extensionId":  "bradlc.vscode-tailwindcss",
        "displayName":  "Tailwind CSS IntelliSense"
    },
    {
        "version":  "2.10.0",
        "name":  "path-intellisense",
        "publisher":  "christian-kohler",
        "source":  "Directory-Enhanced",
        "description":  "Visual Studio Code plugin that autocompletes filenames",
        "extensionId":  "christian-kohler.path-intellisense",
        "displayName":  "Path Intellisense"
    },
    {
        "version":  "3.0.10",
        "name":  "vscode-eslint",
        "publisher":  "dbaeumer",
        "source":  "Directory-Enhanced",
        "description":  "Integrates ESLint JavaScript into VS Code.",
        "extensionId":  "dbaeumer.vscode-eslint",
        "displayName":  "ESLint"
    },
    {
        "version":  "0.6.20",
        "name":  "githistory",
        "publisher":  "donjayamanne",
        "source":  "Directory-Enhanced",
        "description":  "View git log, file history, compare branches or commits",
        "extensionId":  "donjayamanne.githistory",
        "displayName":  "Git History"
    },
    {
        "version":  "2025.7.3005",
        "name":  "gitlens",
        "publisher":  "eamodio",
        "source":  "Directory-Enhanced",
        "description":  "Supercharge Git within VS Code — Visualize code authorship at a glance via Git blame annotations and CodeLens, seamlessly navigate and explore Git repositories, gain valuable insights via rich visualizations and powerful comparison commands, and so much more",
        "extensionId":  "eamodio.gitlens",
        "displayName":  "GitLens — Git supercharged"
    },
    {
        "version":  "3.4.6",
        "name":  "better-comments-next",
        "publisher":  "edwinhuish",
        "source":  "Directory-Enhanced",
        "description":  "Improve your code commenting by annotating with alert, informational, TODOs, and more!",
        "extensionId":  "edwinhuish.better-comments-next",
        "displayName":  "Better Comments Next"
    },
    {
        "version":  "11.0.0",
        "name":  "prettier-vscode",
        "publisher":  "esbenp",
        "source":  "Directory-Enhanced",
        "description":  "Code formatter using prettier",
        "extensionId":  "esbenp.prettier-vscode",
        "displayName":  "Prettier - Code formatter"
    },
    {
        "version":  "0.5.15",
        "name":  "auto-close-tag",
        "publisher":  "formulahendry",
        "source":  "Directory-Enhanced",
        "description":  "Automatically add HTML/XML close tag, same as Visual Studio IDE or Sublime Text",
        "extensionId":  "formulahendry.auto-close-tag",
        "displayName":  "Auto Close Tag"
    },
    {
        "version":  "0.1.10",
        "name":  "auto-rename-tag",
        "publisher":  "formulahendry",
        "source":  "Directory-Enhanced",
        "description":  "Auto rename paired HTML/XML tag",
        "extensionId":  "formulahendry.auto-rename-tag",
        "displayName":  "Auto Rename Tag"
    },
    {
        "version":  "1.3.0",
        "name":  "npm-dependency-links",
        "publisher":  "herrmannplatz",
        "source":  "Directory-Enhanced",
        "description":  "Go to npm site of your dependencies",
        "extensionId":  "herrmannplatz.npm-dependency-links",
        "displayName":  "npm Dependency Links"
    },
    {
        "version":  "1.7.0",
        "name":  "hungry-delete",
        "publisher":  "jasonlhy",
        "source":  "Directory-Enhanced",
        "description":  "To delete an entire block of whitespace or tab, and reduce the time programmers need to press backspace",
        "extensionId":  "jasonlhy.hungry-delete",
        "displayName":  "Hungry Delete"
    },
    {
        "version":  "0.1.3",
        "name":  "two-monokai",
        "publisher":  "khan",
        "source":  "Directory-Enhanced",
        "description":  "Fork of the One Monokai theme",
        "extensionId":  "khan.two-monokai",
        "displayName":  "Two Monokai Theme"
    },
    {
        "version":  "0.32.2",
        "name":  "vscode-gutter-preview",
        "publisher":  "kisstkondoros",
        "source":  "Directory-Enhanced",
        "description":  "Shows image preview in the gutter and on hover",
        "extensionId":  "kisstkondoros.vscode-gutter-preview",
        "displayName":  "Image preview"
    },
    {
        "version":  "1.1.6",
        "name":  "highlight-on-copy",
        "publisher":  "mguellsegarra",
        "source":  "Directory-Enhanced",
        "description":  "Briefly flash and highlight selected copied text",
        "extensionId":  "mguellsegarra.highlight-on-copy",
        "displayName":  "Highlight on Copy"
    },
    {
        "version":  "1.30.0",
        "name":  "git-graph",
        "publisher":  "mhutchie",
        "source":  "Directory-Enhanced",
        "description":  "View a Git Graph of your repository, and perform Git actions from the graph.",
        "extensionId":  "mhutchie.git-graph",
        "displayName":  "Git Graph"
    },
    {
        "version":  "0.1.10",
        "name":  "mosmmy-icons-vscode",
        "publisher":  "moserjose",
        "source":  "Directory-Enhanced",
        "description":  "Official Mosmmy Icons for Mosmmy Theme",
        "extensionId":  "moserjose.mosmmy-icons-vscode",
        "displayName":  "Mosmmy Icons"
    },
    {
        "version":  "1.99.2025041609",
        "name":  "vscode-language-pack-zh-hans",
        "publisher":  "MS-CEINTL",
        "source":  "Directory-Enhanced",
        "description":  "Language pack extension for Chinese (Simplified)",
        "extensionId":  "MS-CEINTL.vscode-language-pack-zh-hans",
        "displayName":  "Chinese (Simplified) (简体中文) Language Pack for Visual Studio Code"
    },
    {
        "version":  "2.8.0",
        "name":  "color-highlight",
        "publisher":  "naumovs",
        "source":  "Directory-Enhanced",
        "description":  "Highlight web colors in your editor",
        "extensionId":  "naumovs.color-highlight",
        "displayName":  "Color Highlight"
    },
    {
        "version":  "0.2.7",
        "name":  "vue3-snippets-plus",
        "publisher":  "owen-dev",
        "source":  "Directory-Enhanced",
        "description":  "vue3 code snippets plus for vscode",
        "extensionId":  "owen-dev.vue3-snippets-plus",
        "displayName":  "vue3 snippets plus"
    },
    {
        "version":  "5.24.0",
        "name":  "material-icon-theme",
        "publisher":  "PKief",
        "source":  "Directory-Enhanced",
        "description":  "Material Design Icons for Visual Studio Code",
        "extensionId":  "PKief.material-icon-theme",
        "displayName":  "Material Icon Theme"
    },
    {
        "version":  "5.7.9",
        "name":  "LiveServer",
        "publisher":  "ritwickdey",
        "source":  "Directory-Enhanced",
        "description":  "Launch a development local Server with live reload feature for static \u0026 dynamic pages",
        "extensionId":  "ritwickdey.LiveServer",
        "displayName":  "Live Server"
    },
    {
        "version":  "0.2.96",
        "name":  "common-intellisense",
        "publisher":  "simonhe",
        "source":  "Directory-Enhanced",
        "description":  "%description%",
        "extensionId":  "simonhe.common-intellisense",
        "displayName":  "%displayName%"
    },
    {
        "version":  "2.8.3",
        "name":  "svg-preview",
        "publisher":  "SimonSiefke",
        "source":  "Directory-Enhanced",
        "description":  "Preview for Svg files",
        "extensionId":  "SimonSiefke.svg-preview",
        "displayName":  "Svg Preview"
    },
    {
        "version":  "4.0.47",
        "name":  "code-spell-checker",
        "publisher":  "streetsidesoftware",
        "source":  "Directory-Enhanced",
        "description":  "Spelling checker for source code",
        "extensionId":  "streetsidesoftware.code-spell-checker",
        "displayName":  "Code Spell Checker"
    },
    {
        "version":  "1.5.3",
        "name":  "vscode-stylelint",
        "publisher":  "stylelint",
        "source":  "Directory-Enhanced",
        "description":  "Official Stylelint extension for Visual Studio Code",
        "extensionId":  "stylelint.vscode-stylelint",
        "displayName":  "Stylelint"
    },
    {
        "version":  "2.0.0",
        "name":  "open-in-browser",
        "publisher":  "techer",
        "source":  "Directory-Enhanced",
        "description":  "This allows you to open the current file in your default browser or application.",
        "extensionId":  "techer.open-in-browser",
        "displayName":  "open in browser"
    },
    {
        "version":  "3.26.0",
        "name":  "errorlens",
        "publisher":  "usernamehw",
        "source":  "Directory-Enhanced",
        "description":  "Improve highlighting of errors, warnings and other language diagnostics.",
        "extensionId":  "usernamehw.errorlens",
        "displayName":  "Error Lens"
    },
    {
        "version":  "0.11.0",
        "name":  "highlight-matching-tag",
        "publisher":  "vincaslt",
        "source":  "Directory-Enhanced",
        "description":  "Highlights matching closing and opening tags",
        "extensionId":  "vincaslt.highlight-matching-tag",
        "displayName":  "Highlight Matching Tag"
    },
    {
        "version":  "3.0.4",
        "name":  "volar",
        "publisher":  "Vue",
        "source":  "Directory-Enhanced",
        "description":  "Language Support for Vue",
        "extensionId":  "Vue.volar",
        "displayName":  "Vue (Official)"
    },
    {
        "version":  "0.6.1",
        "name":  "pretty-ts-errors",
        "publisher":  "YoavBls",
        "source":  "Directory-Enhanced",
        "description":  "Make TypeScript errors prettier and more human-readable in VSCode",
        "extensionId":  "YoavBls.pretty-ts-errors",
        "displayName":  "Pretty TypeScript Errors"
    }
]

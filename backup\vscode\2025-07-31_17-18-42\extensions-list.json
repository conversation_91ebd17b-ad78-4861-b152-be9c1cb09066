﻿[
    {
        "version":  "0.517.0",
        "name":  "vscode-augment",
        "publisher":  "augment",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "augment.vscode-augment",
        "displayName":  "vscode-augment"
    },
    {
        "version":  "0.5.2",
        "name":  "one-monokai",
        "publisher":  "azemoh",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "azemoh.one-monokai",
        "displayName":  "one-monokai"
    },
    {
        "version":  "0.14.25",
        "name":  "vscode-tailwindcss",
        "publisher":  "bradlc",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "bradlc.vscode-tailwindcss",
        "displayName":  "vscode-tailwindcss"
    },
    {
        "version":  "2.10.0",
        "name":  "path-intellisense",
        "publisher":  "christian-kohler",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "christian-kohler.path-intellisense",
        "displayName":  "path-intellisense"
    },
    {
        "version":  "3.0.10",
        "name":  "vscode-eslint",
        "publisher":  "dbaeumer",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "dbaeumer.vscode-eslint",
        "displayName":  "vscode-eslint"
    },
    {
        "version":  "0.6.20",
        "name":  "githistory",
        "publisher":  "donjayamanne",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "donjayamanne.githistory",
        "displayName":  "githistory"
    },
    {
        "version":  "2025.7.3005",
        "name":  "gitlens",
        "publisher":  "eamodio",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "eamodio.gitlens",
        "displayName":  "gitlens"
    },
    {
        "version":  "3.4.6",
        "name":  "better-comments-next",
        "publisher":  "edwinhuish",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "edwinhuish.better-comments-next",
        "displayName":  "better-comments-next"
    },
    {
        "version":  "11.0.0",
        "name":  "prettier-vscode",
        "publisher":  "esbenp",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "esbenp.prettier-vscode",
        "displayName":  "prettier-vscode"
    },
    {
        "version":  "0.5.15",
        "name":  "auto-close-tag",
        "publisher":  "formulahendry",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "formulahendry.auto-close-tag",
        "displayName":  "auto-close-tag"
    },
    {
        "version":  "0.1.10",
        "name":  "auto-rename-tag",
        "publisher":  "formulahendry",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "formulahendry.auto-rename-tag",
        "displayName":  "auto-rename-tag"
    },
    {
        "version":  "1.3.0",
        "name":  "npm-dependency-links",
        "publisher":  "herrmannplatz",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "herrmannplatz.npm-dependency-links",
        "displayName":  "npm-dependency-links"
    },
    {
        "version":  "1.7.0",
        "name":  "hungry-delete",
        "publisher":  "jasonlhy",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "jasonlhy.hungry-delete",
        "displayName":  "hungry-delete"
    },
    {
        "version":  "0.1.3",
        "name":  "two-monokai",
        "publisher":  "khan",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "khan.two-monokai",
        "displayName":  "two-monokai"
    },
    {
        "version":  "0.32.2",
        "name":  "vscode-gutter-preview",
        "publisher":  "kisstkondoros",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "kisstkondoros.vscode-gutter-preview",
        "displayName":  "vscode-gutter-preview"
    },
    {
        "version":  "1.1.6",
        "name":  "highlight-on-copy",
        "publisher":  "mguellsegarra",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "mguellsegarra.highlight-on-copy",
        "displayName":  "highlight-on-copy"
    },
    {
        "version":  "1.30.0",
        "name":  "git-graph",
        "publisher":  "mhutchie",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "mhutchie.git-graph",
        "displayName":  "git-graph"
    },
    {
        "version":  "0.1.10",
        "name":  "mosmmy-icons-vscode",
        "publisher":  "moserjose",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "moserjose.mosmmy-icons-vscode",
        "displayName":  "mosmmy-icons-vscode"
    },
    {
        "version":  "1.99.2025041609",
        "name":  "vscode-language-pack-zh-hans",
        "publisher":  "ms-ceintl",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "ms-ceintl.vscode-language-pack-zh-hans",
        "displayName":  "vscode-language-pack-zh-hans"
    },
    {
        "version":  "2.8.0",
        "name":  "color-highlight",
        "publisher":  "naumovs",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "naumovs.color-highlight",
        "displayName":  "color-highlight"
    },
    {
        "version":  "0.2.7",
        "name":  "vue3-snippets-plus",
        "publisher":  "owen-dev",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "owen-dev.vue3-snippets-plus",
        "displayName":  "vue3-snippets-plus"
    },
    {
        "version":  "5.24.0",
        "name":  "material-icon-theme",
        "publisher":  "pkief",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "pkief.material-icon-theme",
        "displayName":  "material-icon-theme"
    },
    {
        "version":  "5.7.9",
        "name":  "liveserver",
        "publisher":  "ritwickdey",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "ritwickdey.liveserver",
        "displayName":  "liveserver"
    },
    {
        "version":  "0.2.96",
        "name":  "common-intellisense",
        "publisher":  "simonhe",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "simonhe.common-intellisense",
        "displayName":  "common-intellisense"
    },
    {
        "version":  "2.8.3",
        "name":  "svg-preview",
        "publisher":  "simonsiefke",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "simonsiefke.svg-preview",
        "displayName":  "svg-preview"
    },
    {
        "version":  "4.0.47",
        "name":  "code-spell-checker",
        "publisher":  "streetsidesoftware",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "streetsidesoftware.code-spell-checker",
        "displayName":  "code-spell-checker"
    },
    {
        "version":  "1.5.3",
        "name":  "vscode-stylelint",
        "publisher":  "stylelint",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "stylelint.vscode-stylelint",
        "displayName":  "vscode-stylelint"
    },
    {
        "version":  "2.0.0",
        "name":  "open-in-browser",
        "publisher":  "techer",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "techer.open-in-browser",
        "displayName":  "open-in-browser"
    },
    {
        "version":  "3.26.0",
        "name":  "errorlens",
        "publisher":  "usernamehw",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "usernamehw.errorlens",
        "displayName":  "errorlens"
    },
    {
        "version":  "0.11.0",
        "name":  "highlight-matching-tag",
        "publisher":  "vincaslt",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "vincaslt.highlight-matching-tag",
        "displayName":  "highlight-matching-tag"
    },
    {
        "version":  "3.0.4",
        "name":  "volar",
        "publisher":  "vue",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "vue.volar",
        "displayName":  "volar"
    },
    {
        "version":  "0.6.1",
        "name":  "pretty-ts-errors",
        "publisher":  "yoavbls",
        "source":  "CLI",
        "description":  "Retrieved via CLI",
        "extensionId":  "yoavbls.pretty-ts-errors",
        "displayName":  "pretty-ts-errors"
    }
]

[{"key": "alt+t", "command": "extension.varTranslation"}, {"key": "shift+alt+t", "command": "-extension.varTranslation"}, {"key": "ctrl+k", "command": "workbench.action.terminal.kill"}, {"key": "ctrl+shift+oem_3", "command": "workbench.action.terminal.new", "when": "terminalProcessSupported"}, {"key": "ctrl+shift+oem_3", "command": "-workbench.action.terminal.new", "when": "terminalProcessSupported"}, {"key": "ctrl+oem_5", "command": "workbench.action.terminal.split", "when": "terminalFocus && terminalProcessSupported || terminalFocus && terminalWebExtensionContributedProfile"}, {"key": "ctrl+shift+5", "command": "-workbench.action.terminal.split", "when": "terminalFocus && terminalProcessSupported || terminalFocus && terminalWebExtensionContributedProfile"}, {"key": "ctrl+enter", "command": "-github.copilot.generate", "when": "editorTextFocus && github.copilot.activated"}, {"key": "ctrl+alt+oem_5", "command": "github.copilot.generate", "when": "editorTextFocus && github.copilot.activated && !inInteractiveInput && !interactiveEditorFocused"}, {"key": "ctrl+enter", "command": "-github.copilot.generate", "when": "editorTextFocus && github.copilot.activated && !inInteractiveInput && !interactiveEditorFocused"}, {"key": "ctrl+numpad_subtract", "command": "breadcrumbs.revealFocused", "when": "breadcrumbsActive && breadcrumbsVisible"}, {"key": "ctrl+enter", "command": "-breadcrumbs.revealFocused", "when": "breadcrumbsActive && breadcrumbsVisible"}, {"key": "ctrl+enter", "command": "-codegeex.interactive-mode", "when": "editorFocus && !editor<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ctrl+shift+x", "command": "workbench.view.extension.talkx"}, {"key": "ctrl+enter", "command": "-inlineChat.acceptChanges", "when": "inlineChatHasProvider && inlineChatVisible && !inlineChatDocumentChanged || inlineChatHasProvider && inlineChatVisible && config.inlineChat.mode != 'preview'"}, {"key": "ctrl+enter", "command": "-github.copilot.generate", "when": "editorTextFocus && github.copilot.activated && !commentEditorFocused && !inInteractiveInput && !interactiveEditorFocused"}]
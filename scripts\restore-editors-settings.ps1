﻿# 编辑器设置还原脚本
# 从备份文件还原Cursor或VSCode的设置、快捷键、代码片段、扩展等完整配置

param(
    [string]$BackupPath,
    [switch]$Force
)

# 设置控制台编码为UTF-8以支持中文输出
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🔄 开始还原编辑器设置..." -ForegroundColor Green

# 如果没有指定备份路径，则自动扫描并让用户选择
if (-not $BackupPath) {
    $backupRootDir = "backup"

    # 检查备份根目录是否存在
    if (-not (Test-Path $backupRootDir)) {
        Write-Host "❌ 错误：找不到备份根目录: $backupRootDir" -ForegroundColor Red
        Write-Host "💡 提示：请先运行 'yarn backup:editors' 创建备份" -ForegroundColor Yellow
        exit 1
    }

    # 扫描支持的编辑器
    $availableEditors = @()
    $cursorBackupDir = Join-Path $backupRootDir "cursor"
    $vscodeBackupDir = Join-Path $backupRootDir "vscode"
    
    if (Test-Path $cursorBackupDir) {
        $cursorBackups = Get-ChildItem -Path $cursorBackupDir -Directory | Sort-Object Name -Descending
        if ($cursorBackups.Count -gt 0) {
            $availableEditors += @{ Name = "Cursor"; Path = $cursorBackupDir; Backups = $cursorBackups }
        }
    }
    
    if (Test-Path $vscodeBackupDir) {
        $vscodeBackups = Get-ChildItem -Path $vscodeBackupDir -Directory | Sort-Object Name -Descending
        if ($vscodeBackups.Count -gt 0) {
            $availableEditors += @{ Name = "VSCode"; Path = $vscodeBackupDir; Backups = $vscodeBackups }
        }
    }

    if ($availableEditors.Count -eq 0) {
        Write-Host "❌ 错误：没有找到任何编辑器备份" -ForegroundColor Red
        Write-Host "💡 提示：请先运行 'yarn backup:editors' 创建备份" -ForegroundColor Yellow
        exit 1
    }
    
    # 让用户选择编辑器
    Write-Host "`n📋 发现以下编辑器备份:" -ForegroundColor Cyan
    for ($i = 0; $i -lt $availableEditors.Count; $i++) {
        $editor = $availableEditors[$i]
        Write-Host "   [$($i + 1)] $($editor.Name) ($($editor.Backups.Count) 个备份)" -ForegroundColor White
    }
    
    Write-Host ""
    do {
        $editorSelection = Read-Host "请选择要还原的编辑器 (1-$($availableEditors.Count)) 或按 'q' 退出"
        
        if ($editorSelection -eq 'q' -or $editorSelection -eq 'Q') {
            Write-Host "❌ 用户取消操作。" -ForegroundColor Red
            exit 0
        }

        $selectedEditorIndex = $null
        if ([int]::TryParse($editorSelection, [ref]$selectedEditorIndex)) {
            if ($selectedEditorIndex -ge 1 -and $selectedEditorIndex -le $availableEditors.Count) {
                $selectedEditor = $availableEditors[$selectedEditorIndex - 1]
                Write-Host "✅ 已选择编辑器: $($selectedEditor.Name)" -ForegroundColor Green
                break
            }
        }

        Write-Host "⚠️  无效选择，请输入 1-$($availableEditors.Count) 之间的数字或 'q' 退出" -ForegroundColor Yellow
    } while ($true)

    # 获取所有备份文件夹
    $backupFolders = $selectedEditor.Backups

    if ($backupFolders.Count -eq 0) {
        Write-Host "❌ 错误：在 $($selectedEditor.Name) 中没有找到任何备份文件夹" -ForegroundColor Red
        Write-Host "💡 提示：请先运行 'yarn backup:editors' 创建备份" -ForegroundColor Yellow
        exit 1
    }

    # 显示备份列表供用户选择
    Write-Host "`n📋 发现以下备份文件夹:" -ForegroundColor Cyan
    for ($i = 0; $i -lt $backupFolders.Count; $i++) {
        $folder = $backupFolders[$i]
        $backupInfoPath = Join-Path $folder.FullName "backup-info.json"

        if (Test-Path $backupInfoPath) {
            try {
                $backupInfo = Get-Content $backupInfoPath -Raw | ConvertFrom-Json
                $successInfo = "$($backupInfo.SuccessCount)/$($backupInfo.TotalCount) 项"
            } catch {
                $successInfo = "未知"
            }
        } else {
            $successInfo = "未知"
        }

        Write-Host "   [$($i + 1)] $($folder.Name) (成功: $successInfo)" -ForegroundColor White
    }

    # 让用户选择
    Write-Host ""
    do {
        $selection = Read-Host "请选择要还原的备份 (1-$($backupFolders.Count)) 或按 'q' 退出"

        if ($selection -eq 'q' -or $selection -eq 'Q') {
            Write-Host "❌ 用户取消操作。" -ForegroundColor Red
            exit 0
        }

        $selectedIndex = $null
        if ([int]::TryParse($selection, [ref]$selectedIndex)) {
            if ($selectedIndex -ge 1 -and $selectedIndex -le $backupFolders.Count) {
                $BackupPath = $backupFolders[$selectedIndex - 1].FullName
                Write-Host "✅ 已选择备份: $($backupFolders[$selectedIndex - 1].Name)" -ForegroundColor Green
                break
            }
        }

        Write-Host "⚠️  无效选择，请输入 1-$($backupFolders.Count) 之间的数字或 'q' 退出" -ForegroundColor Yellow
    } while ($true)
}

# 检查备份目录是否存在
if (-not (Test-Path $BackupPath)) {
    Write-Host "❌ 错误：找不到备份目录: $BackupPath" -ForegroundColor Red
    exit 1
}

# 检查备份信息文件
$backupInfoPath = Join-Path $BackupPath "backup-info.json"
if (-not (Test-Path $backupInfoPath)) {
    Write-Host "❌ 错误：找不到备份信息文件: $backupInfoPath" -ForegroundColor Red
    exit 1
}

# 读取备份信息
try {
    $backupInfo = Get-Content $backupInfoPath -Raw | ConvertFrom-Json
    Write-Host "📋 备份信息:" -ForegroundColor Cyan
    Write-Host "   备份时间: $($backupInfo.BackupTime)" -ForegroundColor White
    Write-Host "   成功项目: $($backupInfo.SuccessCount)/$($backupInfo.TotalCount)" -ForegroundColor White
} catch {
    Write-Host "❌ 错误：无法读取备份信息: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 确定编辑器类型和配置路径
$editorName = ""
$editorUserPath = ""
$editorCliCommand = ""

# 从备份路径判断编辑器类型
if ($BackupPath -match "\\cursor\\") {
    $editorName = "Cursor"
    $editorUserPath = "$env:APPDATA\Cursor\User"
    $editorCliCommand = "cursor"
} elseif ($BackupPath -match "\\vscode\\") {
    $editorName = "VSCode"
    $editorUserPath = "$env:APPDATA\Code\User"
    $editorCliCommand = "code"
} else {
    # 尝试从备份信息中获取编辑器类型
    try {
        $backupInfo = Get-Content $backupInfoPath -Raw | ConvertFrom-Json
        if ($backupInfo.EditorName) {
            $editorName = $backupInfo.EditorName
            if ($editorName -eq "Cursor") {
                $editorUserPath = "$env:APPDATA\Cursor\User"
                $editorCliCommand = "cursor"
            } elseif ($editorName -eq "VSCode") {
                $editorUserPath = "$env:APPDATA\Code\User"
                $editorCliCommand = "code"
            }
        }
    } catch {
        # 默认使用Cursor
        $editorName = "Cursor"
        $editorUserPath = "$env:APPDATA\Cursor\User"
        $editorCliCommand = "cursor"
    }
}

Write-Host "🎯 检测到编辑器类型: $editorName" -ForegroundColor Cyan

# 检查编辑器配置目录是否存在，不存在则创建
if (-not (Test-Path $editorUserPath)) {
    Write-Host "📁 创建 $editorName 配置目录: $editorUserPath" -ForegroundColor Cyan
    New-Item -ItemType Directory -Path $editorUserPath -Force | Out-Null
}

# 确认还原操作
if (-not $Force) {
    Write-Host "`n⚠️  警告：此操作将覆盖当前的 $editorName 配置！" -ForegroundColor Yellow
    $confirm = Read-Host "是否继续还原？(y/N)"
    if ($confirm -ne 'y' -and $confirm -ne 'Y' -and $confirm -ne 'yes' -and $confirm -ne 'Yes') {
        Write-Host "❌ 用户取消操作。" -ForegroundColor Red
        exit 0
    }
}

$successCount = 0
$totalCount = 0
$successfulExtensions = 0
$failedExtensions = 0

# 还原配置文件
Write-Host "`n📂 开始还原配置文件..." -ForegroundColor Blue
foreach ($item in $backupInfo.BackupItems) {
    if (-not $item.Exists) {
        Write-Host "⏭️  跳过 $($item.Description): 备份中不存在" -ForegroundColor Gray
        continue
    }
    
    $sourcePath = Join-Path $BackupPath $item.Name
    $destPath = Join-Path $editorUserPath $item.Name
    $totalCount++
    
    if (Test-Path $sourcePath) {
        try {
            # 如果目标文件/文件夹已存在，先备份
            if (Test-Path $destPath) {
                $backupName = "$($item.Name).backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
                $backupDestPath = Join-Path $editorUserPath $backupName
                Move-Item -Path $destPath -Destination $backupDestPath -Force
                Write-Host "💾 已备份现有配置: $backupName" -ForegroundColor Yellow
            }
            
            if ($item.Type -eq "Directory") {
                # 复制文件夹
                Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                $fileCount = (Get-ChildItem -Path $sourcePath -Recurse -File).Count
                Write-Host "✅ 已还原 $($item.Description): $($item.Name) ($fileCount 个文件)" -ForegroundColor Green
            } else {
                # 复制文件
                Copy-Item -Path $sourcePath -Destination $destPath -Force
                $fileSize = [math]::Round((Get-Item $sourcePath).Length / 1KB, 2)
                Write-Host "✅ 已还原 $($item.Description): $($item.Name) ($fileSize KB)" -ForegroundColor Green
            }
            $successCount++
        } catch {
            Write-Host "⚠️  还原 $($item.Description) 失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  跳过 $($item.Description): 备份文件不存在" -ForegroundColor Yellow
    }
}

# 还原扩展（优先使用扩展文件，否则从扩展列表下载）
$extensionsDir = Join-Path $BackupPath "extensions"
$extensionsListPath = Join-Path $BackupPath "extensions-list.json"

if (Test-Path $extensionsDir) {
    Write-Host "`n🔌 发现扩展文件，开始安装扩展..." -ForegroundColor Blue
    $extensionFiles = Get-ChildItem -Path $extensionsDir -Filter "*.vsix"
    
    if ($extensionFiles.Count -gt 0) {
        Write-Host "📦 找到 $($extensionFiles.Count) 个扩展文件" -ForegroundColor Cyan
        
        foreach ($extensionFile in $extensionFiles) {
            Write-Host "🔧 安装扩展: $($extensionFile.Name)" -ForegroundColor Cyan

            # 使用 Start-Process 来更好地捕获输出和错误
            $process = Start-Process -FilePath $editorCliCommand -ArgumentList "--install-extension", $extensionFile.FullName -Wait -PassThru -NoNewWindow -RedirectStandardOutput "temp_output.txt" -RedirectStandardError "temp_error.txt"

            if ($process.ExitCode -eq 0) {
                Write-Host "✅ 扩展安装成功: $($extensionFile.BaseName)" -ForegroundColor Green
                $successfulExtensions++
            } else {
                # 读取错误信息
                $errorMessage = ""
                if (Test-Path "temp_error.txt") {
                    $errorMessage = Get-Content "temp_error.txt" -Raw
                }

                # 检查是否是兼容性问题
                if ($errorMessage -match "not compatible with VS Code") {
                    Write-Host "⚠️  扩展安装失败: $($extensionFile.BaseName) - 版本不兼容" -ForegroundColor Yellow
                    Write-Host "   💡 提示: 该扩展可能需要更新版本才能与当前 $editorName 兼容" -ForegroundColor Gray
                } elseif ($errorMessage -match "already installed") {
                    Write-Host "ℹ️  扩展已存在: $($extensionFile.BaseName) - 跳过安装" -ForegroundColor Cyan
                    $successfulExtensions++
                } else {
                    Write-Host "⚠️  扩展安装失败: $($extensionFile.BaseName)" -ForegroundColor Yellow
                    if ($errorMessage.Trim()) {
                        Write-Host "   错误详情: $($errorMessage.Trim())" -ForegroundColor Gray
                    }
                }
                $failedExtensions++
            }

            # 清理临时文件
            if (Test-Path "temp_output.txt") { Remove-Item "temp_output.txt" -Force }
            if (Test-Path "temp_error.txt") { Remove-Item "temp_error.txt" -Force }
        }

        # 显示扩展安装统计
        Write-Host "`n📊 扩展安装统计:" -ForegroundColor Magenta
        Write-Host "   成功安装: $successfulExtensions 个" -ForegroundColor Green
        Write-Host "   安装失败: $failedExtensions 个" -ForegroundColor Yellow
        Write-Host "   总计: $($extensionFiles.Count) 个" -ForegroundColor White
    }
} elseif (Test-Path $extensionsListPath) {
    # 从扩展列表下载并安装扩展
    Write-Host "`n📋 发现扩展列表，开始下载并安装扩展..." -ForegroundColor Blue
    try {
        $extensionList = Get-Content $extensionsListPath -Raw | ConvertFrom-Json
        Write-Host "📦 找到 $($extensionList.Count) 个扩展，开始下载..." -ForegroundColor Cyan
        
        # 创建临时下载目录
        $tempDownloadDir = Join-Path $BackupPath "temp_extensions"
        if (Test-Path $tempDownloadDir) {
            Remove-Item -Path $tempDownloadDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $tempDownloadDir -Force | Out-Null
        
        $downloadSuccess = 0
        $downloadFailed = 0
        $currentIndex = 0
        
        foreach ($extension in $extensionList) {
            $currentIndex++
            $extensionId = $extension.extensionId
            $publisher = $extension.publisher
            $name = $extension.name
            $version = $extension.version
            
            if ($publisher -and $name -and $version) {
                try {
                    # 构造下载URL
                    $downloadUrl = "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/$publisher/vsextensions/$name/$version/vspackage"
                    $fileName = "$extensionId-$version.vsix"
                    $filePath = Join-Path $tempDownloadDir $fileName
                    
                    # 显示进度
                    $progress = [math]::Round(($currentIndex / $extensionList.Count) * 100, 1)
                    Write-Progress -Activity "下载并安装 $editorName 扩展" -Status "$currentIndex/$($extensionList.Count) - $($extension.displayName)" -PercentComplete $progress
                    
                    # 下载文件
                    Write-Host "� 下载: $($extension.displayName)" -ForegroundColor Cyan
                    Invoke-WebRequest -Uri $downloadUrl -OutFile $filePath -ErrorAction Stop
                    
                    # 检查文件是否下载成功
                    if (Test-Path $filePath) {
                        $downloadSuccess++
                        
                        # 立即安装扩展
                        Write-Host "🔧 安装: $($extension.displayName)" -ForegroundColor Cyan
                        $installProcess = Start-Process -FilePath $editorCliCommand -ArgumentList "--install-extension", $filePath -Wait -PassThru -NoNewWindow -RedirectStandardOutput "temp_output.txt" -RedirectStandardError "temp_error.txt"
                        
                        if ($installProcess.ExitCode -eq 0) {
                            Write-Host "✅ 扩展安装成功: $($extension.displayName)" -ForegroundColor Green
                            $successfulExtensions++
                        } else {
                            # 读取错误信息
                            $errorMessage = ""
                            if (Test-Path "temp_error.txt") {
                                $errorMessage = Get-Content "temp_error.txt" -Raw
                            }
                            
                            if ($errorMessage -match "already installed") {
                                Write-Host "ℹ️  扩展已存在: $($extension.displayName) - 跳过安装" -ForegroundColor Cyan
                                $successfulExtensions++
                            } else {
                                Write-Host "⚠️  扩展安装失败: $($extension.displayName)" -ForegroundColor Yellow
                                if ($errorMessage.Trim()) {
                                    Write-Host "   错误详情: $($errorMessage.Trim())" -ForegroundColor Gray
                                }
                                $failedExtensions++
                            }
                        }
                        
                        # 清理临时文件
                        if (Test-Path "temp_output.txt") { Remove-Item "temp_output.txt" -Force }
                        if (Test-Path "temp_error.txt") { Remove-Item "temp_error.txt" -Force }
                        
                        # 删除已安装的扩展文件
                        Remove-Item -Path $filePath -Force
                    }
                } catch {
                    Write-Host "❌ 下载失败: $($extension.displayName) - $($_.Exception.Message)" -ForegroundColor Red
                    $downloadFailed++
                    $failedExtensions++
                }
            } else {
                Write-Host "⚠️  跳过无效扩展: $($extension.displayName)" -ForegroundColor Yellow
                $failedExtensions++
            }
        }
        
        Write-Progress -Activity "下载并安装 $editorName 扩展" -Completed
        
        # 清理临时下载目录
        if (Test-Path $tempDownloadDir) {
            Remove-Item -Path $tempDownloadDir -Recurse -Force
        }
        
        # 显示下载和安装统计
        Write-Host "`n📊 扩展下载和安装统计:" -ForegroundColor Magenta
        Write-Host "   下载成功: $downloadSuccess 个" -ForegroundColor Green
        Write-Host "   下载失败: $downloadFailed 个" -ForegroundColor Red
        Write-Host "   安装成功: $successfulExtensions 个" -ForegroundColor Green
        Write-Host "   安装失败: $failedExtensions 个" -ForegroundColor Yellow
        Write-Host "   总计: $($extensionList.Count) 个" -ForegroundColor White
        
    } catch {
        Write-Host "❌ 读取扩展列表失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n📊 还原完成统计:" -ForegroundColor Magenta
Write-Host "   还原时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host "   备份来源: $BackupPath" -ForegroundColor White
Write-Host "   成功还原: $successCount/$totalCount 项配置" -ForegroundColor White
if ($successfulExtensions -gt 0 -or $failedExtensions -gt 0) {
    Write-Host "   扩展安装: $successfulExtensions 成功, $failedExtensions 失败" -ForegroundColor White
}

if ($successCount -eq $totalCount -and $totalCount -gt 0) {
    Write-Host "`n🎉 所有配置文件还原成功！请重启 $editorName 以应用设置。" -ForegroundColor Green
} elseif ($successCount -gt 0) {
    Write-Host "`n⚠️  部分配置文件还原成功，请检查上述输出并重启 $editorName 。" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ 还原失败，请检查错误信息。" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔄 建议重启 $editorName 以确保所有设置生效。" -ForegroundColor Cyan